// 友情应用页面
Page({
  data: {
    apps: [], // 友情应用列表
    loading: true, // 加载状态
    error: false, // 错误状态
    errorMessage: '', // 错误信息
    isEmpty: false // 是否为空
  },

  onLoad() {
    this.loadFriendApps()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadFriendApps()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadFriendApps().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载友情应用列表
   */
  async loadFriendApps() {
    try {
      this.setData({
        loading: true,
        error: false,
        errorMessage: ''
      })

      const result = await this.callCloudFunction('getFriendApps')
      
      if (result.success) {
        const apps = result.data.apps || []
        this.setData({
          apps,
          isEmpty: apps.length === 0,
          loading: false
        })
      } else {
        throw new Error(result.errMsg || '获取友情应用失败')
      }
    } catch (error) {
      console.error('加载友情应用失败:', error)
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '加载失败，请重试',
        apps: [],
        isEmpty: false
      })
    }
  },

  /**
   * 点击应用卡片
   */
  onAppTap(e) {
    const { app } = e.currentTarget.dataset
    if (!app || !app.navigateParams) {
      wx.showToast({
        title: '应用配置错误',
        icon: 'none'
      })
      return
    }

    this.navigateToMiniProgram(app)
  },

  /**
   * 跳转到小程序
   */
  navigateToMiniProgram(app) {
    const { navigateParams } = app
    
    // 验证必填参数
    if (!navigateParams.appId) {
      wx.showToast({
        title: '应用ID不能为空',
        icon: 'none'
      })
      return
    }

    // 构建跳转参数
    const params = {
      appId: navigateParams.appId,
      success: (res) => {
        console.log('跳转成功:', res)
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        let errorMsg = '跳转失败'
        
        if (err.errMsg) {
          if (err.errMsg.includes('cancel')) {
            errorMsg = '用户取消跳转'
          } else if (err.errMsg.includes('not exist')) {
            errorMsg = '目标小程序不存在'
          } else if (err.errMsg.includes('permission')) {
            errorMsg = '没有跳转权限'
          }
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }
    }

    // 添加可选参数
    if (navigateParams.path) {
      params.path = navigateParams.path
    }
    
    if (navigateParams.extraData && Object.keys(navigateParams.extraData).length > 0) {
      params.extraData = navigateParams.extraData
    }
    
    if (navigateParams.envVersion) {
      params.envVersion = navigateParams.envVersion
    }

    // 执行跳转
    wx.navigateToMiniProgram(params)
  },

  /**
   * 重新加载
   */
  onRetry() {
    this.loadFriendApps()
  },

  /**
   * 调用云函数
   */
  async callCloudFunction(action, data = {}) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: action,
          data: {
            ...data
          }
        }
      })

      return result.result
    } catch (error) {
      console.error('云函数调用失败:', error)
      throw new Error('网络请求失败')
    }
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '发现更多好用的小程序',
      path: '/pages/friend-apps/index'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '发现更多好用的小程序'
    }
  }
})
